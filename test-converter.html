<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test File Converter</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; }
        .file-list { margin: 10px 0; }
        .file-item { padding: 5px; border: 1px solid #eee; margin: 2px 0; }
    </style>
</head>
<body>
    <h1>File Converter Test</h1>
    
    <div class="test-section">
        <h2>Test Multiple File Upload</h2>
        <input type="file" id="test-upload" multiple>
        <button onclick="testFileUpload()">Test Upload</button>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>Test Format Detection</h2>
        <button onclick="testFormatDetection()">Test Format Detection</button>
        <div id="format-results"></div>
    </div>

    <script>
        function testFileUpload() {
            const fileInput = document.getElementById('test-upload');
            const results = document.getElementById('test-results');
            
            if (fileInput.files.length === 0) {
                results.innerHTML = '<p>Please select some files first.</p>';
                return;
            }
            
            let html = '<h3>Selected Files:</h3>';
            for (let i = 0; i < fileInput.files.length; i++) {
                const file = fileInput.files[i];
                const extension = file.name.split('.').pop().toUpperCase();
                html += `<div class="file-item">
                    <strong>${file.name}</strong><br>
                    Size: ${formatFileSize(file.size)}<br>
                    Extension: ${extension}<br>
                    Type: ${file.type || 'Unknown'}
                </div>`;
            }
            results.innerHTML = html;
        }

        function testFormatDetection() {
            const formats = ['JPG', 'PNG', 'PDF', 'DOC', 'MP3', 'MP4'];
            const detected = new Set(['JPG', 'PNG']); // Simulate detected formats
            
            let html = '<h3>Format Filtering Test:</h3>';
            html += '<p><strong>All formats:</strong> ' + formats.join(', ') + '</p>';
            html += '<p><strong>Detected input formats:</strong> ' + Array.from(detected).join(', ') + '</p>';
            html += '<p><strong>Available output formats:</strong> ';
            
            const available = formats.filter(format => !detected.has(format));
            html += available.join(', ') + '</p>';
            
            document.getElementById('format-results').innerHTML = html;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
