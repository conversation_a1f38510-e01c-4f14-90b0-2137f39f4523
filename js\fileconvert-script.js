// File Converter Script
document.addEventListener('DOMContentLoaded', function() {
  console.log('File converter script loaded');
  // DOM Elements
  const dropArea = document.getElementById('drop-area');
  const fileInput = document.getElementById('file-upload');
  const filesList = document.getElementById('files-list');
  const filesContainer = document.getElementById('files-container');
  const filesCount = document.getElementById('files-count');
  const clearAllBtn = document.getElementById('clear-all-btn');
  const convertBtn = document.getElementById('convert-btn');
  const conversionProgress = document.getElementById('conversion-progress');
  const progressList = document.getElementById('progress-list');
  const conversionResults = document.getElementById('conversion-results');
  const resultsList = document.getElementById('results-list');
  const downloadAllBtn = document.getElementById('download-all-btn');
  const newFilesBtn = document.getElementById('new-files-btn');
  const postUploadFormat = document.getElementById('post-upload-format');

  // Check if all required elements exist
  if (!dropArea || !fileInput || !filesList || !filesContainer || !convertBtn) {
    console.error('Required DOM elements not found');
    return;
  }

  // Variables
  let selectedFiles = [];
  let fromFormat = null;
  let toFormat = null;
  let detectedFormats = new Set(); // Track detected input formats

  // File formats with icons
  const fileFormats = {
    // Document formats
    'PDF': { icon: 'fas fa-file-pdf', category: 'document' },
    'DOC': { icon: 'fas fa-file-word', category: 'document' },
    'DOCX': { icon: 'fas fa-file-word', category: 'document' },
    'TXT': { icon: 'fas fa-file-alt', category: 'document' },
    'RTF': { icon: 'fas fa-file-alt', category: 'document' },
    'ODT': { icon: 'fas fa-file-alt', category: 'document' },

    // Image formats
    'JPG': { icon: 'fas fa-file-image', category: 'image' },
    'JPEG': { icon: 'fas fa-file-image', category: 'image' },
    'PNG': { icon: 'fas fa-file-image', category: 'image' },
    'GIF': { icon: 'fas fa-file-image', category: 'image' },
    'BMP': { icon: 'fas fa-file-image', category: 'image' },
    'WEBP': { icon: 'fas fa-file-image', category: 'image' },
    'SVG': { icon: 'fas fa-file-image', category: 'image' },
    'TIFF': { icon: 'fas fa-file-image', category: 'image' },

    // Audio formats
    'MP3': { icon: 'fas fa-file-audio', category: 'audio' },
    'WAV': { icon: 'fas fa-file-audio', category: 'audio' },
    'FLAC': { icon: 'fas fa-file-audio', category: 'audio' },
    'AAC': { icon: 'fas fa-file-audio', category: 'audio' },
    'OGG': { icon: 'fas fa-file-audio', category: 'audio' },
    'M4A': { icon: 'fas fa-file-audio', category: 'audio' },

    // Video formats
    'MP4': { icon: 'fas fa-file-video', category: 'video' },
    'AVI': { icon: 'fas fa-file-video', category: 'video' },
    'MOV': { icon: 'fas fa-file-video', category: 'video' },
    'WMV': { icon: 'fas fa-file-video', category: 'video' },
    'FLV': { icon: 'fas fa-file-video', category: 'video' },
    'MKV': { icon: 'fas fa-file-video', category: 'video' },
    'WEBM': { icon: 'fas fa-file-video', category: 'video' },

    // Archive formats
    'ZIP': { icon: 'fas fa-file-archive', category: 'archive' },
    'RAR': { icon: 'fas fa-file-archive', category: 'archive' },
    '7Z': { icon: 'fas fa-file-archive', category: 'archive' },
    'TAR': { icon: 'fas fa-file-archive', category: 'archive' },
    'GZ': { icon: 'fas fa-file-archive', category: 'archive' },

    // Spreadsheet formats
    'XLS': { icon: 'fas fa-file-excel', category: 'spreadsheet' },
    'XLSX': { icon: 'fas fa-file-excel', category: 'spreadsheet' },
    'CSV': { icon: 'fas fa-file-csv', category: 'spreadsheet' },
    'ODS': { icon: 'fas fa-file-excel', category: 'spreadsheet' },

    // Presentation formats
    'PPT': { icon: 'fas fa-file-powerpoint', category: 'presentation' },
    'PPTX': { icon: 'fas fa-file-powerpoint', category: 'presentation' },
    'ODP': { icon: 'fas fa-file-powerpoint', category: 'presentation' }
  };

  // Initialize dropdowns
  initializeDropdowns();

  // Event Listeners
  fileInput.addEventListener('change', handleFileSelect);
  if (clearAllBtn) clearAllBtn.addEventListener('click', clearAllFiles);
  convertBtn.addEventListener('click', handleConversion);
  if (downloadAllBtn) downloadAllBtn.addEventListener('click', handleDownloadAll);
  if (newFilesBtn) newFilesBtn.addEventListener('click', resetConverter);

  // Initialize dropdown functionality
  function initializeDropdowns() {
    const dropdowns = ['from', 'to', 'post-upload-to'];

    dropdowns.forEach(dropdownId => {
      const btn = document.getElementById(`${dropdownId}-btn`);
      const dropdown = document.getElementById(`${dropdownId}-dropdown`);
      const search = document.getElementById(`${dropdownId}-search`);
      const list = document.getElementById(`${dropdownId}-list`);

      if (!btn || !dropdown || !search || !list) return;

      // Populate format list
      populateFormatList(list, dropdownId);

      // Toggle dropdown
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        closeAllDropdowns();
        dropdown.classList.toggle('show');
        btn.classList.toggle('active');
        if (dropdown.classList.contains('show')) {
          search.focus();
        }
      });

      // Search functionality
      search.addEventListener('input', (e) => {
        filterFormats(list, e.target.value);
      });

      // Format selection
      list.addEventListener('click', (e) => {
        if (e.target.classList.contains('format-item')) {
          const format = e.target.dataset.format;
          const formatText = e.target.textContent.trim();

          // Update button text
          btn.querySelector('.format-text').textContent = formatText;

          // Store selection
          if (dropdownId === 'from') {
            fromFormat = format;
          } else if (dropdownId === 'to' || dropdownId === 'post-upload-to') {
            toFormat = format;
          }

          // Close dropdown
          dropdown.classList.remove('show');
          btn.classList.remove('active');

          // Check if conversion is ready
          checkConversionReady();
        }
      });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', closeAllDropdowns);
  }

  function populateFormatList(list, dropdownId) {
    list.innerHTML = '';
    Object.keys(fileFormats).forEach(format => {
      // For post-upload dropdown, exclude detected input formats
      if (dropdownId === 'post-upload-to' && detectedFormats.has(format)) {
        return;
      }

      const item = document.createElement('div');
      item.className = 'format-item';
      item.dataset.format = format;
      item.innerHTML = `<i class="${fileFormats[format].icon}"></i> ${format}`;
      list.appendChild(item);
    });
  }

  function filterFormats(list, searchTerm) {
    const items = list.querySelectorAll('.format-item');
    items.forEach(item => {
      const text = item.textContent.toLowerCase();
      if (text.includes(searchTerm.toLowerCase())) {
        item.classList.remove('hidden');
      } else {
        item.classList.add('hidden');
      }
    });
  }

  function closeAllDropdowns() {
    document.querySelectorAll('.format-dropdown-content').forEach(dropdown => {
      dropdown.classList.remove('show');
    });
    document.querySelectorAll('.format-btn').forEach(btn => {
      btn.classList.remove('active');
    });
  }

  function checkConversionReady() {
    if (selectedFiles.length > 0 && toFormat) {
      convertBtn.style.display = 'inline-flex';
      convertBtn.disabled = false;
    }
  }

  // Drag and drop functionality
  ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
    dropArea.addEventListener(eventName, preventDefaults, false);
  });
  
  function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
  }
  
  ['dragenter', 'dragover'].forEach(eventName => {
    dropArea.addEventListener(eventName, highlight, false);
  });
  
  ['dragleave', 'drop'].forEach(eventName => {
    dropArea.addEventListener(eventName, unhighlight, false);
  });
  
  function highlight() {
    dropArea.classList.add('drag-over');
  }
  
  function unhighlight() {
    dropArea.classList.remove('drag-over');
  }
  
  dropArea.addEventListener('drop', handleDrop, false);
  
  function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;

    if (files.length) {
      // Convert FileList to Array and add to existing files
      const newFiles = Array.from(files);
      addFiles(newFiles);
    }
  }
  
  // File handling functions
  function handleFileSelect() {
    console.log('handleFileSelect called, files:', fileInput.files.length);
    if (fileInput.files.length) {
      const newFiles = Array.from(fileInput.files);
      console.log('Adding files:', newFiles.map(f => f.name));
      addFiles(newFiles);
    }
  }

  function addFiles(newFiles) {
    console.log('addFiles called with:', newFiles.length, 'files');

    // Add new files to the selected files array
    newFiles.forEach(file => {
      // Check if file already exists (by name and size)
      const exists = selectedFiles.some(existingFile =>
        existingFile.name === file.name && existingFile.size === file.size
      );

      if (!exists) {
        selectedFiles.push(file);
        console.log('Added file:', file.name);

        // Detect file format and add to detected formats
        const fileExtension = getFileExtension(file.name);
        if (fileExtension) {
          detectedFormats.add(fileExtension);
          console.log('Detected format:', fileExtension);
        }
      }
    });

    console.log('Total selected files:', selectedFiles.length);
    console.log('Detected formats:', Array.from(detectedFormats));

    // Update UI
    updateFilesDisplay();
    updateFormatDropdowns();

    // Show post-upload format selection if no format selected
    if (!toFormat && selectedFiles.length > 0) {
      postUploadFormat.style.display = 'block';
    }

    // Check if conversion is ready
    checkConversionReady();
  }

  function getFileExtension(filename) {
    const extension = filename.split('.').pop().toUpperCase();
    return fileFormats[extension] ? extension : null;
  }

  function updateFilesDisplay() {
    if (selectedFiles.length === 0) {
      filesList.style.display = 'none';
      return;
    }

    filesList.style.display = 'block';
    filesCount.textContent = selectedFiles.length;

    // Clear existing file cards
    filesContainer.innerHTML = '';

    // Create file cards
    selectedFiles.forEach((file, index) => {
      const fileCard = createFileCard(file, index);
      filesContainer.appendChild(fileCard);
    });

    // Scroll to files list
    filesList.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
  }

  function createFileCard(file, index) {
    const fileExtension = getFileExtension(file.name);
    const fileIcon = fileExtension ? fileFormats[fileExtension].icon : 'fas fa-file';

    const card = document.createElement('div');
    card.className = 'file-card';
    card.innerHTML = `
      <div class="file-info">
        <div class="file-icon">
          <i class="${fileIcon}"></i>
        </div>
        <div class="file-details">
          <div class="file-name">${file.name}</div>
          <div class="file-meta">
            <span><i class="fas fa-database"></i> ${formatFileSize(file.size)}</span>
            <span><i class="fas fa-file-code"></i> ${fileExtension || 'Unknown'}</span>
          </div>
        </div>
      </div>
      <div class="file-actions">
        <button class="remove-file-btn" onclick="removeFile(${index})" title="Remove file">
          <i class="fas fa-times"></i>
        </button>
      </div>
    `;

    return card;
  }

  function removeFile(index) {
    const removedFile = selectedFiles[index];
    selectedFiles.splice(index, 1);

    // Update detected formats
    updateDetectedFormats();

    // Update UI
    updateFilesDisplay();
    updateFormatDropdowns();

    // Hide post-upload format if no files
    if (selectedFiles.length === 0) {
      postUploadFormat.style.display = 'none';
      convertBtn.style.display = 'none';
    }

    // Check if conversion is ready
    checkConversionReady();
  }

  function clearAllFiles() {
    selectedFiles = [];
    detectedFormats.clear();
    updateFilesDisplay();
    updateFormatDropdowns();
    postUploadFormat.style.display = 'none';
    convertBtn.style.display = 'none';
    fileInput.value = '';
  }

  function updateDetectedFormats() {
    detectedFormats.clear();
    selectedFiles.forEach(file => {
      const fileExtension = getFileExtension(file.name);
      if (fileExtension) {
        detectedFormats.add(fileExtension);
      }
    });
  }

  function updateFormatDropdowns() {
    // Repopulate the post-upload dropdown to exclude detected formats
    const postUploadList = document.getElementById('post-upload-to-list');
    if (postUploadList) {
      populateFormatList(postUploadList, 'post-upload-to');
    }
  }

  // Make removeFile function global so it can be called from HTML
  window.removeFile = removeFile;
  
  function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
  
  function handleConversion() {
    if (selectedFiles.length === 0 || !toFormat) {
      alert('Please select files and output format first.');
      return;
    }

    // Hide convert button and show progress
    convertBtn.style.display = 'none';
    conversionProgress.style.display = 'block';

    // Clear previous progress
    progressList.innerHTML = '';

    // Create progress items for each file
    const progressItems = [];
    selectedFiles.forEach((file, index) => {
      const progressItem = createProgressItem(file, index);
      progressList.appendChild(progressItem);
      progressItems.push(progressItem);
    });

    // Simulate conversion process for each file
    const convertedFiles = [];
    let completedCount = 0;

    selectedFiles.forEach((file, index) => {
      setTimeout(() => {
        // Update progress item status
        const progressItem = progressItems[index];
        const statusElement = progressItem.querySelector('.progress-status');
        statusElement.textContent = 'Converting...';
        statusElement.className = 'progress-status converting';

        // Simulate conversion time
        setTimeout(() => {
          // Mark as complete
          statusElement.textContent = 'Complete';
          statusElement.className = 'progress-status complete';

          // Add to converted files
          convertedFiles.push({
            original: file,
            converted: {
              name: `${file.name.split('.')[0]}.${toFormat.toLowerCase()}`,
              size: Math.floor(file.size * (0.8 + Math.random() * 0.4)), // Simulate size change
              format: toFormat
            }
          });

          completedCount++;

          // Check if all files are converted
          if (completedCount === selectedFiles.length) {
            showConversionResults(convertedFiles);
          }
        }, 1000 + Math.random() * 2000); // Random conversion time
      }, index * 500); // Stagger the start times
    });
  }

  function createProgressItem(file, index) {
    const fileExtension = getFileExtension(file.name);
    const fileIcon = fileExtension ? fileFormats[fileExtension].icon : 'fas fa-file';

    const item = document.createElement('div');
    item.className = 'progress-item';
    item.innerHTML = `
      <div class="progress-info">
        <i class="${fileIcon}"></i>
        <span>${file.name}</span>
      </div>
      <div class="progress-status">Waiting...</div>
    `;

    return item;
  }

  function showConversionResults(convertedFiles) {
    // Hide progress and show results
    conversionProgress.style.display = 'none';
    conversionResults.style.display = 'block';

    // Clear previous results
    resultsList.innerHTML = '';

    // Create result items
    convertedFiles.forEach((fileData, index) => {
      const resultItem = createResultItem(fileData, index);
      resultsList.appendChild(resultItem);
    });

    // Show download buttons
    downloadAllBtn.style.display = 'inline-flex';
    newFilesBtn.style.display = 'inline-flex';

    // Store converted files for download
    window.convertedFiles = convertedFiles;

    // Scroll to results
    conversionResults.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
  }

  function createResultItem(fileData, index) {
    const targetFormat = fileData.converted.format;
    const targetIcon = fileFormats[targetFormat] ? fileFormats[targetFormat].icon : 'fas fa-file';

    const item = document.createElement('div');
    item.className = 'result-item';
    item.innerHTML = `
      <div class="result-info">
        <div class="result-icon">
          <i class="${targetIcon}"></i>
        </div>
        <div class="result-details">
          <div class="result-name">${fileData.converted.name}</div>
          <div class="result-meta">
            <span>Size: ${formatFileSize(fileData.converted.size)}</span> •
            <span>Format: ${targetFormat}</span>
          </div>
        </div>
      </div>
      <button class="download-individual-btn" onclick="downloadIndividualFile(${index})">
        <i class="fas fa-download"></i> Download
      </button>
    `;

    return item;
  }

  function downloadIndividualFile(index) {
    if (!window.convertedFiles || !window.convertedFiles[index]) return;

    const fileData = window.convertedFiles[index];

    // Create a dummy blob for simulation
    const blob = new Blob(['Dummy converted file content'], { type: 'text/plain' });

    // Create download link
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileData.converted.name;
    document.body.appendChild(a);
    a.click();

    // Clean up
    setTimeout(() => {
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    }, 0);
  }

  function handleDownloadAll() {
    if (!window.convertedFiles) return;

    // Download each file individually (in a real app, you might create a ZIP)
    window.convertedFiles.forEach((fileData, index) => {
      setTimeout(() => {
        downloadIndividualFile(index);
      }, index * 200); // Stagger downloads
    });
  }

  // Make functions global for HTML onclick handlers
  window.downloadIndividualFile = downloadIndividualFile;
  

  
  function resetConverter() {
    // Reset state
    selectedFiles = [];
    detectedFormats.clear();
    fromFormat = null;
    toFormat = null;
    fileInput.value = '';

    // Hide all sections
    filesList.style.display = 'none';
    postUploadFormat.style.display = 'none';
    conversionProgress.style.display = 'none';
    conversionResults.style.display = 'none';

    // Reset dropdowns
    document.querySelectorAll('.format-text').forEach(text => {
      text.textContent = 'Select Format';
    });
    document.getElementById('post-upload-to-btn').querySelector('.format-text').textContent = 'Choose format to convert to';

    // Reset UI
    convertBtn.disabled = true;
    convertBtn.innerHTML = '<i class="fas fa-exchange-alt"></i> Convert Files';
    convertBtn.style.display = 'none';
    downloadAllBtn.style.display = 'none';
    newFilesBtn.style.display = 'none';

    // Clear converted files
    window.convertedFiles = null;

    // Update format dropdowns
    updateFormatDropdowns();

    // Close all dropdowns
    closeAllDropdowns();

    // Scroll to top of container
    document.querySelector('.container').scrollIntoView({ behavior: 'smooth', block: 'start' });
  }

  // Initialize
  convertBtn.style.display = 'none';
});
