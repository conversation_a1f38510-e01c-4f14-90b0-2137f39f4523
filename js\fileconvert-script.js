// File Converter Script
document.addEventListener('DOMContentLoaded', function() {
  // DOM Elements
  const dropArea = document.getElementById('drop-area');
  const fileInput = document.getElementById('file-upload');
  const fileDetails = document.getElementById('file-details');
  const fileName = document.getElementById('file-name').querySelector('span');
  const fileSize = document.getElementById('file-size').querySelector('span');
  const fileType = document.getElementById('file-type').querySelector('span');
  const convertBtn = document.getElementById('convert-btn');
  const downloadBtn = document.getElementById('download-btn');
  const newFileBtn = document.getElementById('new-file-btn');
  const postUploadFormat = document.getElementById('post-upload-format');

  // Variables
  let selectedFile = null;
  let fromFormat = null;
  let toFormat = null;

  // File formats with icons
  const fileFormats = {
    // Document formats
    'PDF': { icon: 'fas fa-file-pdf', category: 'document' },
    'DOC': { icon: 'fas fa-file-word', category: 'document' },
    'DOCX': { icon: 'fas fa-file-word', category: 'document' },
    'TXT': { icon: 'fas fa-file-alt', category: 'document' },
    'RTF': { icon: 'fas fa-file-alt', category: 'document' },
    'ODT': { icon: 'fas fa-file-alt', category: 'document' },

    // Image formats
    'JPG': { icon: 'fas fa-file-image', category: 'image' },
    'JPEG': { icon: 'fas fa-file-image', category: 'image' },
    'PNG': { icon: 'fas fa-file-image', category: 'image' },
    'GIF': { icon: 'fas fa-file-image', category: 'image' },
    'BMP': { icon: 'fas fa-file-image', category: 'image' },
    'WEBP': { icon: 'fas fa-file-image', category: 'image' },
    'SVG': { icon: 'fas fa-file-image', category: 'image' },
    'TIFF': { icon: 'fas fa-file-image', category: 'image' },

    // Audio formats
    'MP3': { icon: 'fas fa-file-audio', category: 'audio' },
    'WAV': { icon: 'fas fa-file-audio', category: 'audio' },
    'FLAC': { icon: 'fas fa-file-audio', category: 'audio' },
    'AAC': { icon: 'fas fa-file-audio', category: 'audio' },
    'OGG': { icon: 'fas fa-file-audio', category: 'audio' },
    'M4A': { icon: 'fas fa-file-audio', category: 'audio' },

    // Video formats
    'MP4': { icon: 'fas fa-file-video', category: 'video' },
    'AVI': { icon: 'fas fa-file-video', category: 'video' },
    'MOV': { icon: 'fas fa-file-video', category: 'video' },
    'WMV': { icon: 'fas fa-file-video', category: 'video' },
    'FLV': { icon: 'fas fa-file-video', category: 'video' },
    'MKV': { icon: 'fas fa-file-video', category: 'video' },
    'WEBM': { icon: 'fas fa-file-video', category: 'video' },

    // Archive formats
    'ZIP': { icon: 'fas fa-file-archive', category: 'archive' },
    'RAR': { icon: 'fas fa-file-archive', category: 'archive' },
    '7Z': { icon: 'fas fa-file-archive', category: 'archive' },
    'TAR': { icon: 'fas fa-file-archive', category: 'archive' },
    'GZ': { icon: 'fas fa-file-archive', category: 'archive' },

    // Spreadsheet formats
    'XLS': { icon: 'fas fa-file-excel', category: 'spreadsheet' },
    'XLSX': { icon: 'fas fa-file-excel', category: 'spreadsheet' },
    'CSV': { icon: 'fas fa-file-csv', category: 'spreadsheet' },
    'ODS': { icon: 'fas fa-file-excel', category: 'spreadsheet' },

    // Presentation formats
    'PPT': { icon: 'fas fa-file-powerpoint', category: 'presentation' },
    'PPTX': { icon: 'fas fa-file-powerpoint', category: 'presentation' },
    'ODP': { icon: 'fas fa-file-powerpoint', category: 'presentation' }
  };

  // Initialize dropdowns
  initializeDropdowns();

  // Event Listeners
  fileInput.addEventListener('change', handleFileSelect);
  convertBtn.addEventListener('click', handleConversion);
  downloadBtn.addEventListener('click', handleDownload);
  newFileBtn.addEventListener('click', resetConverter);

  // Initialize dropdown functionality
  function initializeDropdowns() {
    const dropdowns = ['from', 'to', 'post-upload-to'];

    dropdowns.forEach(dropdownId => {
      const btn = document.getElementById(`${dropdownId}-btn`);
      const dropdown = document.getElementById(`${dropdownId}-dropdown`);
      const search = document.getElementById(`${dropdownId}-search`);
      const list = document.getElementById(`${dropdownId}-list`);

      if (!btn || !dropdown || !search || !list) return;

      // Populate format list
      populateFormatList(list, dropdownId);

      // Toggle dropdown
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        closeAllDropdowns();
        dropdown.classList.toggle('show');
        btn.classList.toggle('active');
        if (dropdown.classList.contains('show')) {
          search.focus();
        }
      });

      // Search functionality
      search.addEventListener('input', (e) => {
        filterFormats(list, e.target.value);
      });

      // Format selection
      list.addEventListener('click', (e) => {
        if (e.target.classList.contains('format-item')) {
          const format = e.target.dataset.format;
          const formatText = e.target.textContent.trim();

          // Update button text
          btn.querySelector('.format-text').textContent = formatText;

          // Store selection
          if (dropdownId === 'from') {
            fromFormat = format;
          } else if (dropdownId === 'to' || dropdownId === 'post-upload-to') {
            toFormat = format;
          }

          // Close dropdown
          dropdown.classList.remove('show');
          btn.classList.remove('active');

          // Check if conversion is ready
          checkConversionReady();
        }
      });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', closeAllDropdowns);
  }

  function populateFormatList(list, dropdownId) {
    list.innerHTML = '';
    Object.keys(fileFormats).forEach(format => {
      const item = document.createElement('div');
      item.className = 'format-item';
      item.dataset.format = format;
      item.innerHTML = `<i class="${fileFormats[format].icon}"></i> ${format}`;
      list.appendChild(item);
    });
  }

  function filterFormats(list, searchTerm) {
    const items = list.querySelectorAll('.format-item');
    items.forEach(item => {
      const text = item.textContent.toLowerCase();
      if (text.includes(searchTerm.toLowerCase())) {
        item.classList.remove('hidden');
      } else {
        item.classList.add('hidden');
      }
    });
  }

  function closeAllDropdowns() {
    document.querySelectorAll('.format-dropdown-content').forEach(dropdown => {
      dropdown.classList.remove('show');
    });
    document.querySelectorAll('.format-btn').forEach(btn => {
      btn.classList.remove('active');
    });
  }

  function checkConversionReady() {
    if (selectedFile && toFormat) {
      convertBtn.style.display = 'inline-flex';
      convertBtn.disabled = false;
    }
  }

  // Drag and drop functionality
  ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
    dropArea.addEventListener(eventName, preventDefaults, false);
  });
  
  function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
  }
  
  ['dragenter', 'dragover'].forEach(eventName => {
    dropArea.addEventListener(eventName, highlight, false);
  });
  
  ['dragleave', 'drop'].forEach(eventName => {
    dropArea.addEventListener(eventName, unhighlight, false);
  });
  
  function highlight() {
    dropArea.classList.add('drag-over');
  }
  
  function unhighlight() {
    dropArea.classList.remove('drag-over');
  }
  
  dropArea.addEventListener('drop', handleDrop, false);
  
  function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;
    
    if (files.length) {
      fileInput.files = files;
      handleFileSelect();
    }
  }
  
  // File handling functions
  function handleFileSelect() {
    if (fileInput.files.length) {
      selectedFile = fileInput.files[0];
      displayFileDetails(selectedFile);

      // Show post-upload format selection if no format selected
      if (!toFormat) {
        postUploadFormat.style.display = 'block';
      }

      // Check if conversion is ready
      checkConversionReady();
    }
  }
  
  function displayFileDetails(file) {
    // Show file details card
    fileDetails.style.display = 'block';
    
    // Update file details
    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);
    fileType.textContent = file.type || 'Unknown';
    
    // Scroll to file details
    fileDetails.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
  }
  
  function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
  
  function handleConversion() {
    if (!selectedFile || !toFormat) {
      alert('Please select a file and output format first.');
      return;
    }

    // This is a simulation - in a real app, you would perform actual conversion
    convertBtn.disabled = true;
    convertBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Converting...';

    // Simulate conversion process
    setTimeout(() => {
      convertBtn.style.display = 'none';
      downloadBtn.style.display = 'inline-flex';
      newFileBtn.style.display = 'inline-flex';

      // Show success message
      const successMessage = document.createElement('div');
      successMessage.className = 'comment-box';
      successMessage.style.backgroundColor = '#e8f5e9'; // Light green
      successMessage.style.borderLeftColor = '#4caf50'; // Green
      successMessage.style.color = '#2e7d32'; // Dark green

      successMessage.innerHTML = `
        <h3><i class="fas fa-check-circle"></i> Conversion Complete</h3>
        <p>Your file has been successfully converted to ${toFormat.toUpperCase()}. Click the download button to save it.</p>
      `;

      // Insert before the action buttons
      document.querySelector('.action-buttons').before(successMessage);

      // Scroll to success message
      successMessage.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }, 2000);
  }
  
  function handleDownload() {
    // In a real app, this would download the actual converted file
    // For this demo, we'll just simulate a download

    // Create a dummy blob
    const blob = new Blob(['Dummy file content'], { type: 'text/plain' });

    // Create download link
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${selectedFile.name.split('.')[0]}.${toFormat.toLowerCase()}`;
    document.body.appendChild(a);
    a.click();

    // Clean up
    setTimeout(() => {
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    }, 0);
  }
  
  function resetConverter() {
    // Reset state
    selectedFile = null;
    fromFormat = null;
    toFormat = null;
    fileInput.value = '';
    fileDetails.style.display = 'none';
    postUploadFormat.style.display = 'none';

    // Reset dropdowns
    document.querySelectorAll('.format-text').forEach(text => {
      text.textContent = 'Select Format';
    });
    document.getElementById('post-upload-to-btn').querySelector('.format-text').textContent = 'Choose format to convert to';

    // Reset UI
    convertBtn.disabled = true;
    convertBtn.innerHTML = '<i class="fas fa-exchange-alt"></i> Convert File';
    convertBtn.style.display = 'none';
    downloadBtn.style.display = 'none';
    newFileBtn.style.display = 'none';

    // Remove success message if exists
    const successMessage = document.querySelector('.comment-box');
    if (successMessage && successMessage.querySelector('h3').textContent.includes('Conversion Complete')) {
      successMessage.remove();
    }

    // Close all dropdowns
    closeAllDropdowns();

    // Scroll to top of container
    document.querySelector('.container').scrollIntoView({ behavior: 'smooth', block: 'start' });
  }

  // Initialize
  convertBtn.style.display = 'none';
});
