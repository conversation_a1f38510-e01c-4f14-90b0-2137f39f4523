:root {
  font-size: 16px;
/* CSS Variables for colors and styling - Pure Monochrome Theme */
  --primary-color: #0a0a0a;
  --secondary-color: #141414;
  --accent-color: #2a2a2a;
  --accent-light: #505050;
  --text-color: #e0e0e0;
  --highlight-color: #ffffff;
  --highlight-color-rgb: 255, 255, 255;
  --danger-color: #707070;
  --success-color: #909090;
  --card-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
  --transition-speed: 0.3s;
  --border-color: rgba(255, 255, 255, 0.08);
  --slider-track-height: 6px;
  --slider-thumb-size: 20px;
  --card-gradient: linear-gradient(145deg, rgba(30, 30, 30, 0.4) 0%, rgba(10, 10, 10, 0.2) 100%);
  --card-highlight: rgba(255, 255, 255, 0.03);

  /* Mobile-specific variables */
  --mobile-padding: 16px;
  --mobile-border-radius: 12px;
  --mobile-button-height: 50px;
  --mobile-input-height: 48px;
}

*{
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Poppins', sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--primary-color);
  color: var(--text-color);
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  transition: background-color var(--transition-speed) ease;
  overflow-x: hidden;
  width: 100%;
  position: relative;
}

/* Background texture */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 15% 50%, rgba(255, 255, 255, 0.03) 0%, transparent 25%),
    radial-gradient(circle at 85% 30%, rgba(255, 255, 255, 0.03) 0%, transparent 25%),
    linear-gradient(135deg, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
  opacity: 0.8;
  z-index: -2;
  pointer-events: none;
  animation: pulseGradient 15s ease infinite alternate;
}

body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cpath fill='%23ffffff' fill-opacity='0.02' d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z'/%3E%3C/svg%3E"),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(to right, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 150px 150px, 40px 40px, 40px 40px;
  z-index: -1;
  opacity: 0.5;
  pointer-events: none;
  animation: floatingParticles 60s linear infinite;
}

/* Floating particles animation */
/* .particle {
  position: fixed;
  width: 5px;
  height: 5px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  pointer-events: none;
  z-index: -1;
  animation: float 15s infinite ease-in-out;
} */

/* @keyframes pulseGradient {
  0% {
    background-position: 0% 0%;
    opacity: 0.5;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    background-position: 100% 100%;
    opacity: 0.5;
  }
}

@keyframes floatingParticles {
  0% {
    background-position: 0 0, 0 0, 0 0;
  }
  100% {
    background-position: 500px 500px, 40px 40px, 40px 40px;
  }
}

@keyframes float {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg);
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: translateY(-100vh) translateX(100px) rotate(360deg);
    opacity: 0;
  }
} */

body.menu-open {
  overflow: hidden;
}

/* Navigation Bar */
nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #000000;
  padding: 12px 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(5px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

nav .logo {
  display: flex;
  align-items: center;
}

nav .logo a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text-color);
}

nav .logo img {
  height: 50px;
  transition: transform var(--transition-speed) ease;
}

nav .logo:hover img {
  transform: scale(1.05);
}

.logo-text {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
}

.logo-line {
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--text-color);
  letter-spacing: 0.5px;
}

.logo-pro {
  font-weight: 800;
  font-size: 1.5rem;
  color: var(--text-color);
  line-height: 1;
}

.menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.menu-toggle .bar {
  width: 25px;
  height: 3px;
  background-color: var(--text-color);
  margin: 4px 0;
  transition: transform 0.4s ease, opacity 0.3s ease;
  border-radius: 3px;
}

.menu-toggle.active .bar:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.menu-toggle.active .bar:nth-child(2) {
  opacity: 0;
}

.menu-toggle.active .bar:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

.nav-links {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
}

.nav-links li {
  margin-left: 20px;
  position: relative;
}

.nav-links li a {
  color: var(--text-color);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all var(--transition-speed) ease;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 25px;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}


.nav-links li a:hover {
  background: var(--highlight-color);
  color: var(--primary-color);
  transform: translateY(-2px);
}

/* Remove the underline effect */
.nav-links li::after {
  display: none;
}

.nav-links li:hover::after {
  display: none;
}

/* Dropdown Menu Styles */
.nav-links li.dropdown {
  position: relative;
}

.nav-links li.dropdown > a {
  display: flex;
  align-items: center;
  gap: 5px;
  position: relative;
  padding-right: 20px;
}

.nav-links li.dropdown > a i {
  font-size: 0.8rem;
  transition: transform var(--transition-speed) ease;
}

.nav-links li.dropdown:hover > a i {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(10px);
  background-color: var(--secondary-color);
  min-width: 220px;
  box-shadow: var(--card-shadow);
  border-radius: 8px;
  padding: 10px 0;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-speed) ease;
  z-index: 101;
  border: 1px solid var(--border-color);
}

.dropdown-menu::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid var(--secondary-color);
  z-index: 102;
}

.nav-links li.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

.dropdown-menu li {
  margin: 0;
  width: 100%;
}

.dropdown-menu li a {
  padding: 12px 15px;
  display: flex;
  align-items: center;
  text-align: left;
  font-size: 0.95rem;
  transition: all var(--transition-speed) ease;
  border-left: 3px solid transparent;
}

.dropdown-menu li a i {
  margin-right: 10px;
  width: 16px;
  text-align: center;
}

.dropdown-menu li a:hover {
  background-color: var(--accent-color);
  border-left: 3px solid var(--highlight-color);
  padding-left: 20px;
}

.dropdown-menu li::after {
  display: none;
}

/* Main Content */
.container {
  text-align: center;
  padding: 30px 30px 80px;
  flex: 1;
  width: 80%;
  max-width: 1200px;
  margin: 0 auto;
  box-sizing: border-box;
}

.hero-section {
  position: relative;
  margin-bottom: 60px;
  padding: 40px 20px;
  animation: fadeIn 1s ease-in-out;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.03) 0%, transparent 70%),
    radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.03) 0%, transparent 70%);
  z-index: 1;
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 2;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

h1 {
  font-size: 2.8rem;
  margin-bottom: 15px;
  color: var(--text-color);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-logo {
  display: inline-block;
  vertical-align: middle;
  animation: pulse 2s infinite alternate;
}

@keyframes pulse {
  0% { transform: scale(1); }
  100% { transform: scale(1.05); }
}

.heading-text {
  font-size: 2.5rem;
  font-weight: 700;
  margin-left: 15px;
  background: linear-gradient(to right, var(--text-color), var(--highlight-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.3rem;
  color: var(--accent-light);
  margin-bottom: 20px;
  font-weight: 300;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-description {
  font-size: 1.1rem;
  color: var(--accent-light);
  margin: 0 auto 30px;
  max-width: 700px;
  line-height: 1.6;
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 25px;
  flex-wrap: wrap;
}

.hero-feature {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(40, 40, 40, 0.5);
  padding: 8px 15px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.hero-feature:hover {
  transform: translateY(-3px);
  background-color: rgba(60, 60, 60, 0.5);
}

.hero-feature i {
  color: var(--highlight-color);
  font-size: 0.9rem;
}

.upload-box {
  border: 2px dashed var(--accent-color);
  border-radius: 12px;
  padding: 40px;
  background-color: var(--secondary-color);
  background-image: var(--card-gradient);
  width: 80%;
  margin: 0 auto 40px;
  position: relative;
  box-shadow: var(--card-shadow);
  transition: all var(--transition-speed) ease;
  overflow: hidden;
  box-sizing: border-box;
  text-align: center;
}

.upload-box.highlight {
  border-color: var(--highlight-color);
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5);
}

.upload-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 70% 30%, var(--card-highlight) 0%, transparent 60%);
  opacity: 0.5;
  z-index: 0;
  pointer-events: none;
}

.upload-icon {
  font-size: 3rem;
  color: var(--accent-light);
  margin-bottom: 20px;
  animation: float 3s infinite ease-in-out;
}

@keyframes float {
  0% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0); }
}

.upload-box:hover {
  transform: translateY(-5px);
}

.upload-label {
  display: inline-block;
  padding: 14px 28px;
  background-color: var(--accent-color);
  color: var(--text-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  z-index: 2;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  margin-bottom: 10px;
}

.upload-label::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: all 0.6s ease;
  z-index: -1;
}

.upload-label:hover {
  background-color: var(--accent-light);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

.upload-label:hover::before {
  left: 100%;
}

.upload-label i {
  margin-right: 8px;
  font-size: 1.1rem;
}

.drag-text {
  color: var(--accent-light);
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.upload-info {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: center;
  position: relative;
  z-index: 2;
}

.formats, .max-size {
  font-size: 0.9rem;
  color: var(--accent-light);
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Removed duplicate details-grid class */

.upload-instructions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.upload-instructions p {
  font-size: 0.85rem;
  color: var(--accent-light);
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: rgba(65, 90, 119, 0.2);
  padding: 5px 10px;
  border-radius: 5px;
  transition: all var(--transition-speed) ease;
}

.upload-instructions p:hover {
  background-color: rgba(65, 90, 119, 0.4);
  transform: translateY(-2px);
}

input[type="file"] {
  display: none;
}

#image-preview {
  margin-top: 25px;
  position: relative;
  z-index: 1;
}

#image-preview img {
  max-width: 100%;
  max-height: 250px;
  border-radius: 10px;
  box-shadow: var(--card-shadow);
  transition: all var(--transition-speed) ease;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

#image-preview img:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.details-card {
  margin-top: 25px;
  font-size: 0.9rem;
  color: var(--text-color);
  text-align: left;
  display: none; /* Hidden by default */
  animation: slideUp 0.5s ease-out;
  background-color: var(--secondary-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.details-header {
  background-color: var(--secondary-color);
  color: var(--text-color);
  font-size: 1.1rem;
  font-weight: 600;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 1px solid var(--border-color);
}

.details-header i {
  color: var(--text-color);
  font-size: 1.2rem;
}

.details-content {
  padding: 15px;
  background-color: var(--secondary-color);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

#image-details p {
  margin: 0;
  display: flex;
  align-items: center;
  color: var(--accent-light);
  font-size: 0.95rem;
  padding: 5px 0;
}

#image-details p i {
  margin-right: 10px;
  color: var(--highlight-color);
  width: 20px;
  text-align: center;
  font-size: 1.1rem;
  opacity: 0.8;
}

.action-button {
  padding: 12px 25px;
  background-color: var(--accent-color);
  color: var(--text-color);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all var(--transition-speed) ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  text-decoration: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  min-height: 44px;
}

.action-button:hover {
  background-color: var(--accent-light);
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.action-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.action-button.secondary {
  background-color: var(--secondary-color);
  border: 1px solid var(--accent-color);
}

.action-button.secondary:hover {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.action-button i {
  font-size: 1.1rem;
}

.pulse-animation {
  animation: pulse-button 2s infinite;
}

@keyframes pulse-button {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.2);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

.result-container {
  margin-top: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  width: 80%;
  max-width: 80%;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  margin-bottom: 60px;
  display: none; /* Hidden by default, will be shown after compression */
}

.preview-container {
  width: 100%;
  padding: 20px;
  background-color: var(--secondary-color);
  background-image: var(--card-gradient);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  transition: all var(--transition-speed) ease;
  position: relative;
  overflow: hidden;
}

.preview-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 70% 20%, var(--card-highlight) 0%, transparent 30%);
  opacity: 0.5;
  z-index: 0;
  pointer-events: none;
}

.preview-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(135deg, transparent 0%, var(--card-highlight) 50%, transparent 100%);
  background-size: 200% 200%;
  opacity: 0.1;
  animation: gradientMove 15s ease infinite alternate;
  pointer-events: none;
  z-index: 0;
}

@keyframes gradientMove {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}

/* How It Works Section */
.how-it-works {
  width: 90%;
  max-width: 1000px;
  margin: 80px auto;
  text-align: center;
  position: relative;
}

.how-it-works h2 {
  font-size: 2.2rem;
  margin-bottom: 50px;
  color: var(--text-color);
  position: relative;
  display: inline-block;
}

.how-it-works h2 i {
  margin-right: 15px;
  color: var(--accent-light);
}

.steps-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
  margin-bottom: 60px;
}

.step {
  flex: 1;
  min-width: 200px;
  max-width: 280px;
  background-color: var(--secondary-color);
  background-image: var(--card-gradient);
  border-radius: 12px;
  padding: 30px 20px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  transition: all var(--transition-speed) ease;
  position: relative;
  overflow: hidden;
}

.step::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--accent-color), var(--accent-light));
  opacity: 0.7;
  z-index: 1;
}

.step::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 50% 0%, var(--card-highlight) 0%, transparent 60%);
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
  z-index: 0;
}

.step:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
}

.step:hover::after {
  opacity: 0.6;
  animation: pulseGlow 2s infinite alternate;
}

.step-icon {
  position: relative;
  width: 70px;
  height: 70px;
  background-color: var(--accent-color);
  border-radius: 50%;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.step-icon i {
  font-size: 1.8rem;
  color: var(--text-color);
}

.step-number {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 25px;
  height: 25px;
  background-color: var(--accent-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
  color: var(--text-color);
  border: 2px solid var(--secondary-color);
}

.step h3 {
  margin: 15px 0;
  font-size: 1.3rem;
  color: var(--text-color);
}

.step p {
  color: var(--accent-light);
  font-size: 0.95rem;
  line-height: 1.5;
}

.tech-info {
  background-color: var(--secondary-color);
  background-image: var(--card-gradient);
  border-radius: 12px;
  padding: 30px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  text-align: left;
  position: relative;
  overflow: hidden;
}

.tech-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 90% 10%, var(--card-highlight) 0%, transparent 50%),
    radial-gradient(circle at 10% 90%, var(--card-highlight) 0%, transparent 40%);
  pointer-events: none;
  opacity: 0.5;
  z-index: 0;
}

.tech-info::after {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 300%;
  height: 300%;
  background:
    repeating-conic-gradient(
      var(--card-highlight) 0deg,
      transparent 5deg,
      transparent 10deg,
      var(--card-highlight) 15deg
    );
  opacity: 0.03;
  animation: rotateConic 60s linear infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes rotateConic {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.tech-info h3 {
  font-size: 1.4rem;
  margin-bottom: 15px;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  z-index: 1;
}

.tech-info h3 i {
  color: var(--accent-light);
}

.tech-info p {
  color: var(--accent-light);
  font-size: 1rem;
  line-height: 1.6;
  position: relative;
  z-index: 1;
}

.blog-promo {
  margin-top: 25px;
  padding: 15px 20px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  border: 1px solid var(--border-color);
  display: inline-block;
  position: relative;
  z-index: 1;
}

.blog-promo p {
  margin: 0;
  font-size: 1.05rem;
}

.blog-promo i {
  color: var(--highlight-color);
  margin-right: 8px;
}

.blog-promo a {
  color: var(--highlight-color);
  font-weight: 600;
  text-decoration: none;
  border-bottom: 1px dotted var(--highlight-color);
  transition: all 0.3s ease;
}

.blog-promo a:hover {
  border-bottom: 1px solid var(--highlight-color);
}

#preview img {
  max-width: 100%;
  max-height: 350px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-speed) ease;
  border: 2px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 1;
}

#preview img:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
  width: 100%;
}

.result-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-color);
  font-size: 1.2rem;
}

.result-header h3 i {
  color: var(--highlight-color);
}

.comparison-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  color: var(--accent-light);
}

/* Switch Toggle */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--accent-color);
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: var(--text-color);
  transition: .4s;
}

input:checked + .slider {
  background-color: var(--highlight-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

.preview-wrapper {
  position: relative;
  margin-bottom: 30px;
}

.preview-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(20, 20, 20, 0.8);
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  backdrop-filter: blur(5px);
  border: 1px solid var(--border-color);
  z-index: 10; /* Ensure it appears in front of the image */
}

.preview-stats {
  display: flex;
  gap: 15px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-item i {
  color: var(--highlight-color);
  font-size: 1rem;
}

.stat-item span {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
}

.stat-label {
  font-size: 0.7rem;
  color: var(--accent-light);
}

.result-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.size-comparison {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  width: 100%;
}

.size-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  background-color: var(--secondary-color);
  padding: 10px 20px;
  border-radius: 8px;
  min-width: 150px;
  box-shadow: var(--card-shadow);
}

.size-label {
  font-size: 0.8rem;
  color: var(--accent-light);
}

.size-comparison i.fa-arrow-right {
  color: var(--highlight-color);
  font-size: 1.5rem;
}

#compressed-size-display, #original-size-display {
  font-size: 1rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

#compressed-size-display i, #original-size-display i {
  color: var(--accent-light);
}

.target-accuracy {
  background-color: rgba(40, 40, 40, 0.5);
  padding: 8px 15px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  color: var(--highlight-color);
  font-size: 0.9rem;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
}

#download-link {
  pointer-events: none;
  opacity: 0.5;
}

#download-link.active {
  pointer-events: auto;
  opacity: 1;
}

/* Settings Section */
.settings-card {
  background-color: var(--secondary-color);
  background-image: var(--card-gradient);
  border-radius: 12px;
  padding: 25px;
  margin: 30px auto;
  width: 80%;
  max-width: 80%;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.settings-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  z-index: 0;
  pointer-events: none;
}

.settings-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: 200% 100%;
  opacity: 0.2;
  animation: shimmer 8s infinite linear;
  pointer-events: none;
  z-index: 0;
}

.settings-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.settings-card h3 {
  color: var(--text-color);
  font-size: 1.2rem;
  margin-bottom: 15px;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
  position: relative;
  z-index: 1;
  width: 100%;
}

.settings-card h3 i {
  color: var(--highlight-color);
}

.settings-tabs {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.settings-tab {
  padding: 10px 18px;
  background-color: var(--accent-color);
  border: none;
  border-radius: 8px;
  color: var(--text-color);
  cursor: pointer;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.settings-tab i {
  font-size: 0.9rem;
}

.settings-tab:hover {
  background-color: var(--accent-light);
  transform: translateY(-2px);
}

.settings-tab.active {
  background-color: var(--highlight-color);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.settings-panel {
  display: none;
  animation: fadeIn 0.3s ease;
}

.settings-panel.active {
  display: block;
}

.settings {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  position: relative;
  z-index: 1;
  padding: 10px 0;
}

.compression-level-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  width: 100%;
}

.compression-level-container label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--text-color);
}

.slider-container {
  width: 100%;
  max-width: 550px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 20px 0 30px;
  margin: 20px auto;
}

.setting-label {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 30px;
  font-weight: 500;
  color: var(--text-color);
  font-size: 1rem;
}

.slider-track {
  position: relative;
  height: 7px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  width: 100%;
  margin-bottom: 35px;
}

.slider-fill {
  position: absolute;
  height: 100%;
  background-color: var(--highlight-color);
  border-radius: 5px;
  width: 70%; /* Default value */
  transition: width 0.2s ease;
  z-index: 1;
}

.slider-thumb {
  position: absolute;
  width: 30px;
  height: 30px;
  background-color: white;
  border-radius: 50%;
  top: 50%;
  left: 70%; /* Default value - will be updated by JS */
  transform: translate(-50%, -50%);
  z-index: 3;
  pointer-events: none;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
  transition: transform 0.2s ease;
}

.slider-thumb.active {
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

input[type="range"] {
  width: 100%;
  height: 30px;
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  outline: none;
  cursor: pointer;
  margin: 0;
  padding: 0;
  position: absolute;
  top: -10px;
  left: 0;
  z-index: 2;
}

.compression-value-container {
  text-align: center;
  margin-top: 10px;
  padding: 5px 0;
}

/* Hide default slider thumb */
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: transparent;
  cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 50%;
  background: transparent;
  cursor: pointer;
}

input[type="range"]::-ms-thumb {
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 50%;
  background: transparent;
  cursor: pointer;
}

/* Hide default track */
input[type="range"]::-webkit-slider-runnable-track {
  height: 10px;
  background: transparent;
}

input[type="range"]::-moz-range-track {
  height: 10px;
  background: transparent;
}

input[type="range"]::-ms-track {
  height: 10px;
  background: transparent;
  border: none;
  color: transparent;
}

#compression-value {
  font-weight: 500;
  color: var(--text-color);
  font-size: 1.5rem;
  margin-top: 15px;
  text-align: center;
}

.divider {
  width: 80%;
  display: flex;
  align-items: center;
  margin: 0;
}

.divider::before {
  content: '';
  flex: 1;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.1);
}

.or-text {
  font-size: 1rem;
  color: var(--accent-light);
  margin: 0 15px;
  font-weight: 500;
}

.dimension-inputs {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.aspect-ratio-link {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-light);
  margin-top: 25px;
  animation: pulse 2s infinite;
}

.aspect-ratio-link i {
  font-size: 1.2rem;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

.setting {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--text-color);
}

.dimension-input {
  background: rgba(6, 6, 6, 0.7);
  border: 1px solid white;
  color: var(--text-color);
  padding: 10px 15px;
  border-radius: 8px;
  width: 150px;
  max-width: 100%;
  font-size: 1rem;
  transition: all var(--transition-speed) ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) inset;
  box-sizing: border-box;
}

.dimension-input:focus {
  border-color: var(--highlight-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(76, 201, 240, 0.3);
}

.dimension-input:hover {
  border-color: var(--highlight-color);
}

.target-size-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.target-size-container .setting {
  width: 100%;
  max-width: 350px;
}

.target-size-container .dimension-input {
  width: 100%;
}

.size-hint {
  font-size: 0.85rem;
  color: var(--accent-light);
  text-align: center;
  margin-top: 5px;
  opacity: 0.8;
}

/* Footer */
/* Footer Styles */
footer {
  padding: 60px 20px 30px;
  background-color: var(--secondary-color);
  color: var(--text-color);
  margin-top: 80px;
  position: relative;
  border-top: 1px solid var(--border-color);
  box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 10% 10%, var(--card-highlight) 0%, transparent 60%),
    radial-gradient(circle at 90% 90%, var(--card-highlight) 0%, transparent 60%);
  opacity: 0.05;
  z-index: 0;
  pointer-events: none;
}

.footer-content {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1.5fr 1fr 1fr 1fr;
  gap: 30px;
  position: relative;
  z-index: 1;
}

.footer-column {
  display: flex;
  flex-direction: column;
}

.footer-column:first-child {
  grid-column: 1;
}

.footer-column:nth-child(2) {
  grid-column: 2;
}

.footer-column:nth-child(3) {
  grid-column: 3;
}

.footer-column:nth-child(4) {
  grid-column: 4;
}

.footer-logo {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.footer-logo img {
  height: 70px;
  transition: transform 0.3s ease;
}

.footer-logo:hover img {
  transform: scale(1.05);
}

.footer-description {
  color: var(--accent-light);
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 25px;
  max-width: 300px;
}

.footer-heading {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--text-color);
  position: relative;
  padding-bottom: 10px;
}

.footer-heading::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(to right, var(--highlight-color), transparent);
  border-radius: 3px;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer-links a {
  color: var(--accent-light);
  text-decoration: none;
  transition: all var(--transition-speed) ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
  width: fit-content;
}

.footer-links a i {
  font-size: 0.8rem;
  color: var(--highlight-color);
  transition: transform 0.3s ease;
}

.footer-links a:hover {
  color: var(--highlight-color);
  transform: translateX(5px);
}

.footer-links a:hover i {
  transform: translateX(3px);
}

.social-links {
  display: flex;
  gap: 15px;
  margin-top: 5px;
}

.social-link {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-light);
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-link:hover {
  background-color: var(--highlight-color);
  color: black;
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.footer-divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--border-color), transparent);
  margin: 30px 0;
  grid-column: 1 / -1;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  grid-column: 1 / -1;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.copyright {
  font-size: 0.9rem;
  color: var(--accent-light);
}

.copyright a {
  color: var(--highlight-color);
  text-decoration: none;
  transition: color var(--transition-speed) ease;
}

.copyright a:hover {
  text-decoration: underline;
}

.back-to-top {
  position: fixed;
  right: 30px;
  bottom: 30px;
  width: 55px;
  height: 55px;
  background: linear-gradient(135deg, var(--highlight-color), var(--accent-color));
  color: black;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.back-to-top:active {
  transform: translateY(-3px) scale(0.95);
}

.back-to-top i {
  transition: transform 0.3s ease;
}

.back-to-top:hover i {
  transform: translateY(-3px);
}

/* Responsive Footer */
@media (max-width: 1200px) {
  .footer-content {
    grid-template-columns: 1.2fr 1fr 1fr 1fr;
    padding: 0 20px;
  }
}

@media (max-width: 992px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 40px 30px;
  }

  .footer-column:first-child {
    grid-column: 1 / -1;
    text-align: center;
    align-items: center;
  }

  .footer-column:nth-child(2) {
    grid-column: 1;
  }

  .footer-column:nth-child(3) {
    grid-column: 2;
  }

  .footer-column:nth-child(4) {
    grid-column: 1 / -1;
    text-align: center;
    align-items: center;
    margin-top: 10px;
  }

  .footer-description {
    max-width: 500px;
  }

  .footer-column:nth-child(4) .footer-heading::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .footer-column:nth-child(4) .footer-links {
    align-items: center;
  }
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 35px;
  }

  .footer-column {
    grid-column: 1 / -1 !important;
    text-align: center;
    align-items: center;
  }

  .footer-heading::after {
    left: 50% !important;
    transform: translateX(-50%) !important;
  }

  .footer-links {
    align-items: center;
  }

  .footer-links a {
    justify-content: center;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .back-to-top {
    right: 20px;
    bottom: 20px;
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 480px) {
  footer {
    padding: 50px 15px 30px;
  }

  .footer-content {
    gap: 30px;
  }

  .social-links {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* Tooltip styles */
.tooltip {
  position: relative;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 120px;
  background-color: rgba(0, 0, 0, 0.8);
  color: var(--text-color);
  text-align: center;
  border-radius: 6px;
  padding: 5px 10px;
  position: absolute;
  z-index: 20;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s, visibility 0.3s;
  font-size: 0.8rem;
  font-weight: normal;
  pointer-events: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--border-color);
}

/* Special positioning for settings tabs tooltips */
.settings-tab.tooltip .tooltip-text {
  bottom: 130%;
  font-weight: 400;
}

.tooltip .tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

