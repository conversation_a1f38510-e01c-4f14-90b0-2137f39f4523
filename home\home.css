/* ImgNinja Landing Page Styles */

/* Import main styles */
@import url('../css/styles.css');

/* Monochrome Theme Override */
:root {
  /* Monochrome Color Palette */
  --primary-color: #000000;
  --secondary-color: #1a1a1a;
  --accent-color: #333333;
  --accent-light: #666666;
  --text-color: #ffffff;
  --border-color: #333333;
  --highlight-color: #ffffff;
  --card-bg: #1a1a1a;
  --card-highlight: #2a2a2a;

  /* Gradients */
  --hero-gradient: linear-gradient(135deg, #000000 0%, #333333 100%);
  --feature-gradient: linear-gradient(135deg, #1a1a1a 0%, #333333 100%);
  --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  --card-hover-shadow: 0 20px 40px rgba(0, 0, 0, 0.7);
}

/* Global Body Override */
body {
  background-color: var(--primary-color);
  color: var(--text-color);
}

/* Navigation Override - Copy from global styles */
nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #000000;
  padding: 12px 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(5px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

nav .logo {
  display: flex;
  align-items: center;
}

nav .logo a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text-color);
}

nav .logo img {
  height: 50px;
  transition: transform 0.3s ease;
}

nav .logo:hover img {
  transform: scale(1.05);
}

.menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.menu-toggle .bar {
  width: 25px;
  height: 3px;
  background-color: var(--text-color);
  margin: 4px 0;
  transition: transform 0.4s ease, opacity 0.3s ease;
  border-radius: 3px;
}

.menu-toggle.active .bar:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.menu-toggle.active .bar:nth-child(2) {
  opacity: 0;
}

.menu-toggle.active .bar:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

.nav-links {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
}

.nav-links li {
  margin-left: 20px;
  position: relative;
}

.nav-links li a {
  color: var(--text-color);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 25px;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.nav-links li a:hover {
  background: var(--highlight-color);
  color: var(--primary-color);
  transform: translateY(-2px);
}

/* Remove the underline effect */
.nav-links li::after {
  display: none;
}

.nav-links li:hover::after {
  display: none;
}

/* Hero Section */
.hero-landing {
  min-height: 100vh;
  background: var(--hero-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding-top: 80px; /* Account for fixed navbar */
}

.hero-landing::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.05"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
  opacity: 0.3;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
  text-align: center;
  color: white;
  z-index: 2;
  position: relative;
  max-width: 800px;
  padding: 0 20px;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: var(--text-color);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  animation: slideInUp 1s ease-out;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  animation: slideInUp 1s ease-out 0.2s both;
}

.hero-description {
  font-size: 1.1rem;
  margin-bottom: 3rem;
  opacity: 0.8;
  line-height: 1.6;
  animation: slideInUp 1s ease-out 0.4s both;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  animation: slideInUp 1s ease-out 0.6s both;
}

.btn-primary {
  background: var(--highlight-color);
  color: var(--primary-color);
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  display: inline-flex;
  align-items: center;
  gap: 10px;
  border: 2px solid var(--highlight-color);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  background: transparent;
  color: var(--highlight-color);
}

.btn-secondary {
  background: transparent;
  color: var(--text-color);
  padding: 15px 30px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 10px;
}

.btn-secondary:hover {
  background: var(--highlight-color);
  color: var(--primary-color);
  border-color: var(--highlight-color);
  transform: translateY(-3px);
}

/* Features Section */
.features-section {
  padding: 100px 0;
  background: var(--secondary-color);
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  text-align: center;
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.section-subtitle {
  text-align: center;
  font-size: 1.2rem;
  color: var(--accent-light);
  margin-bottom: 4rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.feature-card {
  background: var(--card-bg);
  padding: 2rem;
  border-radius: 20px;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--highlight-color);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--card-hover-shadow);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: var(--accent-color);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--highlight-color);
  margin-bottom: 1.5rem;
  border: 2px solid var(--border-color);
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.feature-description {
  color: var(--accent-light);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.feature-status {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.status-live {
  background: var(--highlight-color);
  color: var(--primary-color);
}

.status-coming {
  background: var(--accent-color);
  color: var(--text-color);
}

/* Stats Section */
.stats-section {
  padding: 80px 0;
  background: var(--secondary-color);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.stat-item {
  text-align: center;
  color: var(--text-color);
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  color: var(--highlight-color);
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.stat-label {
  font-size: 1.1rem;
  color: var(--accent-light);
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .menu-toggle {
    display: flex;
  }

  .nav-links {
    position: fixed;
    top: 74px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 74px);
    background: #000000;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 2rem;
    transition: left 0.3s ease;
    gap: 1rem;
    z-index: 99;
  }

  .nav-links.active {
    left: 0;
  }

  .nav-links li {
    margin: 0;
    width: 100%;
    text-align: center;
  }

  .nav-links li a {
    font-size: 1.1rem;
    padding: 15px 25px;
    width: 200px;
    text-align: center;
    justify-content: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .stat-number {
    font-size: 2rem;
  }
}

/* Additional Landing Page Enhancements */
.hero-landing {
  background-attachment: fixed;
}

/* Loading Animation */
body:not(.loaded) .hero-content > * {
  opacity: 0;
  transform: translateY(30px);
}

body.loaded .hero-content > * {
  transition: all 0.8s ease-out;
}

/* Feature Card Hover Effects */
.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
  transition: all 0.3s ease;
}

.feature-card:hover .feature-title {
  color: var(--highlight-color);
  transition: color 0.3s ease;
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* Gradient Text Animation */
.hero-title {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Enhanced Button Animations */
.btn-primary, .btn-secondary {
  position: relative;
  overflow: hidden;
}

.btn-primary::before, .btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before, .btn-secondary:hover::before {
  left: 100%;
}

/* Stats Counter Animation Enhancement */
.stat-item {
  position: relative;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: var(--highlight-color);
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.stat-item.animate-in::before {
  opacity: 1;
}

/* Improved Mobile Experience */
@media (max-width: 768px) {
  .hero-landing {
    background-attachment: scroll;
  }

  .scroll-indicator {
    display: none;
  }
}
